<mat-form-field appearance="outline" class="w-100">
  <mat-label>{{ label }}</mat-label>
  
  <input 
    matInput
    [type]="type"
    [placeholder]="placeholder"
    [value]="value || ''"
    [disabled]="disabled || isDisabled"
    [readonly]="readonly"
    [required]="required"
    [minlength]="minLength"
    [maxlength]="maxLength"
    [pattern]="pattern"
    [autocomplete]="autocomplete"
    [id]="id"
    [name]="name"
    (input)="onInputChange($event.target?.value)"
    (blur)="onBlur($event)"
    (focus)="onFocus($event)"
  />
  
  <mat-hint *ngIf="hint && !hasError()">{{ hint }}</mat-hint>
  
  <mat-error *ngIf="hasError()">
    {{ getErrorMessage() }}
  </mat-error>
</mat-form-field>
