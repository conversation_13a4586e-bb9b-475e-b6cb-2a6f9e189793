// Primary Color Palette
$primary-color: #2196F3; // Material Blue
$primary-light: #64B5F6; // Lighter shade
$primary-dark: #1976D2; // Darker shade
$accent-color: #FF4081; // Material Pink
$accent-light: #FF80AB; // Light accent
$accent-dark: #C2185B; // Dark accent

// Secondary Colors
$secondary-color: #f5f5f5; // Very light gray
$secondary-dark: #424242; // Dark gray

// Text Colors
$text-dark: #212121; // Primary text color
$text-medium: #757575; // Secondary text color
$text-light: #FFFFFF; // White text for dark backgrounds
$text-muted: #9E9E9E; // Disabled/hint text color

// Background Colors
$bg-light: #FFFFFF;
$bg-off-white: #FAFAFA;
$bg-light-gray: #F5F5F5;
$bg-dark: #303030; // Dark background

// Border Colors
$border-light: #E0E0E0;
$border-medium: #BDBDBD;
$border-dark: $primary-color;

// Shadow
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
$shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
$shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;
$spacing-3xl: 64px;

// Breakpoints
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1400px;

// Container
$container-max-width: 1200px;

// Border Radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-full: 50%;

// Transitions
$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// Z-index
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// Typography
$font-family-base: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
$font-size-xs: 0.75rem;   // 12px
$font-size-sm: 0.875rem;  // 14px
$font-size-base: 1rem;    // 16px
$font-size-lg: 1.125rem;  // 18px
$font-size-xl: 1.25rem;   // 20px
$font-size-2xl: 1.5rem;   // 24px
$font-size-3xl: 1.875rem; // 30px
$font-size-4xl: 2.25rem;  // 36px

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// Status Colors
$success-color: #4CAF50;
$success-light: #81C784;
$success-dark: #388E3C;

$warning-color: #FF9800;
$warning-light: #FFB74D;
$warning-dark: #F57C00;

$error-color: #F44336;
$error-light: #E57373;
$error-dark: #D32F2F;

$info-color: #2196F3;
$info-light: #64B5F6;
$info-dark: #1976D2;
