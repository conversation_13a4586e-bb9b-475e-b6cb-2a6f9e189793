using GenericSpaTemplate.Models;
using Microsoft.AspNetCore.Mvc;

namespace GenericSpaTemplate.Controllers;

/// <summary>
/// Sample controller demonstrating API patterns and response wrapping
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProductsController : ControllerBase
{
    private readonly ILogger<ProductsController> _logger;

    // Sample in-memory data for demonstration
    private static readonly List<ProductDto> _products = new()
    {
        new ProductDto { Id = 1, Name = "Laptop", Description = "High-performance laptop", Price = 999.99m, Category = "Electronics", IsAvailable = true },
        new ProductDto { Id = 2, Name = "Mouse", Description = "Wireless optical mouse", Price = 29.99m, Category = "Electronics", IsAvailable = true },
        new ProductDto { Id = 3, Name = "Keyboard", Description = "Mechanical keyboard", Price = 79.99m, Category = "Electronics", IsAvailable = false },
        new ProductDto { Id = 4, Name = "Monitor", Description = "4K display monitor", Price = 299.99m, Category = "Electronics", IsAvailable = true },
        new ProductDto { Id = 5, Name = "Headphones", Description = "Noise-cancelling headphones", Price = 199.99m, Category = "Electronics", IsAvailable = true }
    };

    public ProductsController(ILogger<ProductsController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Get all products
    /// </summary>
    /// <returns>List of products</returns>
    [HttpGet]
    public ActionResult<IEnumerable<ProductDto>> GetProducts()
    {
        _logger.LogInformation("Getting all products");
        return Ok(_products);
    }

    /// <summary>
    /// Get a specific product by ID
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Product details</returns>
    [HttpGet("{id}")]
    public ActionResult<ProductDto> GetProduct(int id)
    {
        _logger.LogInformation("Getting product with ID: {ProductId}", id);
        
        var product = _products.FirstOrDefault(p => p.Id == id);
        if (product == null)
        {
            _logger.LogWarning("Product with ID {ProductId} not found", id);
            return NotFound();
        }

        return Ok(product);
    }

    /// <summary>
    /// Create a new product
    /// </summary>
    /// <param name="createProductDto">Product creation data</param>
    /// <returns>Created product</returns>
    [HttpPost]
    public ActionResult<ProductDto> CreateProduct([FromBody] CreateProductDto createProductDto)
    {
        _logger.LogInformation("Creating new product: {ProductName}", createProductDto.Name);

        var newProduct = new ProductDto
        {
            Id = _products.Max(p => p.Id) + 1,
            Name = createProductDto.Name,
            Description = createProductDto.Description,
            Price = createProductDto.Price,
            Category = createProductDto.Category,
            IsAvailable = createProductDto.IsAvailable,
            CreatedAt = DateTime.UtcNow
        };

        _products.Add(newProduct);

        return CreatedAtAction(nameof(GetProduct), new { id = newProduct.Id }, newProduct);
    }

    /// <summary>
    /// Update an existing product
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <param name="updateProductDto">Product update data</param>
    /// <returns>Updated product</returns>
    [HttpPut("{id}")]
    public ActionResult<ProductDto> UpdateProduct(int id, [FromBody] UpdateProductDto updateProductDto)
    {
        _logger.LogInformation("Updating product with ID: {ProductId}", id);

        var product = _products.FirstOrDefault(p => p.Id == id);
        if (product == null)
        {
            _logger.LogWarning("Product with ID {ProductId} not found for update", id);
            return NotFound();
        }

        // Update only provided fields
        if (!string.IsNullOrEmpty(updateProductDto.Name))
            product.Name = updateProductDto.Name;
        
        if (!string.IsNullOrEmpty(updateProductDto.Description))
            product.Description = updateProductDto.Description;
        
        if (updateProductDto.Price.HasValue)
            product.Price = updateProductDto.Price.Value;
        
        if (!string.IsNullOrEmpty(updateProductDto.Category))
            product.Category = updateProductDto.Category;
        
        if (updateProductDto.IsAvailable.HasValue)
            product.IsAvailable = updateProductDto.IsAvailable.Value;

        product.UpdatedAt = DateTime.UtcNow;

        return Ok(product);
    }

    /// <summary>
    /// Delete a product
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Success confirmation</returns>
    [HttpDelete("{id}")]
    public ActionResult DeleteProduct(int id)
    {
        _logger.LogInformation("Deleting product with ID: {ProductId}", id);

        var product = _products.FirstOrDefault(p => p.Id == id);
        if (product == null)
        {
            _logger.LogWarning("Product with ID {ProductId} not found for deletion", id);
            return NotFound();
        }

        _products.Remove(product);
        return NoContent();
    }

    /// <summary>
    /// Search products by category
    /// </summary>
    /// <param name="category">Product category</param>
    /// <returns>Filtered products</returns>
    [HttpGet("category/{category}")]
    public ActionResult<IEnumerable<ProductDto>> GetProductsByCategory(string category)
    {
        _logger.LogInformation("Getting products by category: {Category}", category);

        var products = _products.Where(p => 
            p.Category.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();

        return Ok(products);
    }
}
