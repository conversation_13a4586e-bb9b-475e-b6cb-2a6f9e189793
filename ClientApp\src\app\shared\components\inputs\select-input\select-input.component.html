<mat-form-field appearance="outline" class="w-100">
  <mat-label>{{ label }}</mat-label>
  
  <mat-select 
    [value]="value"
    [disabled]="disabled || isDisabled"
    [required]="required"
    [multiple]="multiple"
    [placeholder]="placeholder"
    (selectionChange)="onSelectionChange($event.value)">
    
    <mat-option *ngFor="let option of options" 
                [value]="option.value" 
                [disabled]="option.disabled">
      {{ option.label }}
    </mat-option>
  </mat-select>
</mat-form-field>
