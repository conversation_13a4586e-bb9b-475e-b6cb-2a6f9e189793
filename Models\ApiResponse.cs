using System.Collections.Generic;
using System.Net;

namespace GenericSpaTemplate.Models
{
    /// <summary>
    /// Standard API response wrapper
    /// </summary>
    /// <typeparam name="T">Type of data being returned</typeparam>
    public class ApiResponse<T>
    {
        /// <summary>
        /// Indicates if the request was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Message providing additional information about the response
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// The data returned by the API
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// HTTP status code
        /// </summary>
        public int StatusCode { get; set; } = 200;

        /// <summary>
        /// Detailed error information (only populated when Success is false)
        /// </summary>
        public Dictionary<string, string[]>? Errors { get; set; }

        /// <summary>
        /// Creates a successful response with data
        /// </summary>
        public static ApiResponse<T> SuccessResponse(T data, string? message = null, int statusCode = (int)HttpStatusCode.OK)
        {
            return new ApiResponse<T>
            {
                Success = true,
                Message = message,
                Data = data,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Creates a failure response
        /// </summary>
        public static ApiResponse<T> FailureResponse(string message, int statusCode = (int)HttpStatusCode.BadRequest)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Data = default,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Creates a failure response with detailed errors
        /// </summary>
        public static ApiResponse<T> FailureResponse(string message, Dictionary<string, string[]> errors, int statusCode = (int)HttpStatusCode.BadRequest)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Data = default,
                Errors = errors,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Creates a not found response
        /// </summary>
        public static ApiResponse<T> NotFoundResponse(string message = "Resource not found")
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Data = default,
                StatusCode = (int)HttpStatusCode.NotFound
            };
        }
    }

    /// <summary>
    /// Non-generic version of ApiResponse for when no data needs to be returned
    /// </summary>
    public class ApiResponse
    {
        /// <summary>
        /// Indicates if the request was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Message providing additional information about the response
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// HTTP status code
        /// </summary>
        public int StatusCode { get; set; } = 200;

        /// <summary>
        /// Detailed error information (only populated when Success is false)
        /// </summary>
        public Dictionary<string, string[]>? Errors { get; set; }

        /// <summary>
        /// Creates a successful response
        /// </summary>
        public static ApiResponse SuccessResponse(string? message = null, int statusCode = (int)HttpStatusCode.OK)
        {
            return new ApiResponse
            {
                Success = true,
                Message = message,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Creates a failure response
        /// </summary>
        public static ApiResponse FailureResponse(string message, int statusCode = (int)HttpStatusCode.BadRequest)
        {
            return new ApiResponse
            {
                Success = false,
                Message = message,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Creates a failure response with detailed errors
        /// </summary>
        public static ApiResponse FailureResponse(string message, Dictionary<string, string[]> errors, int statusCode = (int)HttpStatusCode.BadRequest)
        {
            return new ApiResponse
            {
                Success = false,
                Message = message,
                Errors = errors,
                StatusCode = statusCode
            };
        }

        /// <summary>
        /// Creates a not found response
        /// </summary>
        public static ApiResponse NotFoundResponse(string message = "Resource not found")
        {
            return new ApiResponse
            {
                Success = false,
                Message = message,
                StatusCode = (int)HttpStatusCode.NotFound
            };
        }
    }
}
