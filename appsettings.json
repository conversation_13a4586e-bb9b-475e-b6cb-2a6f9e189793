{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=GenericSpaTemplateDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Swagger": {"Title": "Generic SPA Template API", "Version": "v1", "Description": "A comprehensive Angular and .NET SPA template with clean architecture patterns", "ContactName": "API Support", "ContactEmail": "<EMAIL>"}, "AllowedHosts": "*"}