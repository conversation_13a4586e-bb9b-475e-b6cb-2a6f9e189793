/* Import theme variables and Angular Material theme */
@use 'assets/scss/variables' as vars;
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Global styles */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: vars.$font-family-base;
  color: vars.$text-dark;
  background-color: vars.$bg-off-white;
  line-height: vars.$line-height-normal;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 vars.$spacing-md 0;
  font-weight: vars.$font-weight-medium;
  line-height: vars.$line-height-tight;
}

h1 {
  font-size: vars.$font-size-4xl;
}

h2 {
  font-size: vars.$font-size-3xl;
}

h3 {
  font-size: vars.$font-size-2xl;
}

h4 {
  font-size: vars.$font-size-xl;
}

h5 {
  font-size: vars.$font-size-lg;
}

h6 {
  font-size: vars.$font-size-base;
}

p {
  margin: 0 0 vars.$spacing-md 0;
}

/* Links */
a {
  color: vars.$primary-color;
  text-decoration: none;
  transition: color vars.$transition-normal;

  &:hover {
    color: vars.$primary-dark;
    text-decoration: underline;
  }
}

/* Form elements */
.form-row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 -#{vars.$spacing-sm};

  @media (max-width: vars.$breakpoint-md) {
    flex-direction: column;
    margin: 0;
  }
}

/* Grid column classes */
.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
  padding: 0 vars.$spacing-sm;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 vars.$spacing-sm;

  @media (max-width: vars.$breakpoint-md) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 vars.$spacing-sm;

  @media (max-width: vars.$breakpoint-md) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0 vars.$spacing-sm;

  @media (max-width: vars.$breakpoint-md) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Material Design overrides */
.mat-mdc-form-field {
  width: 100%;
  margin-bottom: vars.$spacing-md;
}

.mat-mdc-card {
  margin-bottom: vars.$spacing-md;
  border-radius: vars.$border-radius-md;
  box-shadow: vars.$shadow-sm;
  overflow: hidden;
}

.mat-mdc-raised-button.mat-primary {
  background-color: vars.$primary-color;
  color: vars.$text-light;
}

.mat-mdc-stroked-button.mat-primary {
  color: vars.$primary-color;
  border-color: vars.$primary-color;
}

/* Utility classes */
.text-primary {
  color: vars.$primary-color;
}

.text-accent {
  color: vars.$accent-color;
}

.text-success {
  color: vars.$success-color;
}

.text-warning {
  color: vars.$warning-color;
}

.text-error {
  color: vars.$error-color;
}

.text-muted {
  color: vars.$text-muted;
}

.bg-primary {
  background-color: vars.$primary-color;
  color: vars.$text-light;
}

.bg-light {
  background-color: vars.$bg-light;
}

.bg-light-gray {
  background-color: vars.$bg-light-gray;
}

.shadow-sm {
  box-shadow: vars.$shadow-sm;
}

.shadow-md {
  box-shadow: vars.$shadow-md;
}

.shadow-lg {
  box-shadow: vars.$shadow-lg;
}

.rounded {
  border-radius: vars.$border-radius-md;
}

.rounded-sm {
  border-radius: vars.$border-radius-sm;
}

.rounded-lg {
  border-radius: vars.$border-radius-lg;
}

/* Spacing utilities */
.m-0 {
  margin: 0;
}

.m-1 {
  margin: vars.$spacing-xs;
}

.m-2 {
  margin: vars.$spacing-sm;
}

.m-3 {
  margin: vars.$spacing-md;
}

.m-4 {
  margin: vars.$spacing-lg;
}

.m-5 {
  margin: vars.$spacing-xl;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: vars.$spacing-xs;
}

.p-2 {
  padding: vars.$spacing-sm;
}

.p-3 {
  padding: vars.$spacing-md;
}

.p-4 {
  padding: vars.$spacing-lg;
}

.p-5 {
  padding: vars.$spacing-xl;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: vars.$spacing-xs;
}

.mt-2 {
  margin-top: vars.$spacing-sm;
}

.mt-3 {
  margin-top: vars.$spacing-md;
}

.mt-4 {
  margin-top: vars.$spacing-lg;
}

.mt-5 {
  margin-top: vars.$spacing-xl;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: vars.$spacing-xs;
}

.mb-2 {
  margin-bottom: vars.$spacing-sm;
}

.mb-3 {
  margin-bottom: vars.$spacing-md;
}

.mb-4 {
  margin-bottom: vars.$spacing-lg;
}

.mb-5 {
  margin-bottom: vars.$spacing-xl;
}

/* Container */
.container {
  max-width: vars.$container-max-width;
  margin: 0 auto;
  padding: 0 vars.$spacing-lg;

  @media (max-width: vars.$breakpoint-md) {
    padding: 0 vars.$spacing-md;
  }
}

/* Flexbox utilities */
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Text alignment */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Provide sufficient contrast against white background */
a {
  color: #0366d6;
}

code {
  color: #e01a76;
}

.btn-primary {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}