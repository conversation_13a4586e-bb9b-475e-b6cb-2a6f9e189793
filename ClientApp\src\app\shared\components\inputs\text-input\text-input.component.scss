@use '../../../../../assets/scss/variables' as vars;

:host {
  display: block;
  width: 100%;
}

.mat-mdc-form-field {
  width: 100%;
  
  &.mat-form-field-invalid {
    .mat-mdc-form-field-outline {
      color: vars.$error-color;
    }
  }
}

.mat-mdc-input-element {
  &:disabled {
    color: vars.$text-muted;
  }
}

// Custom styling for different input types
input[type="email"] {
  &:invalid {
    box-shadow: none; // Remove browser default validation styling
  }
}

input[type="number"] {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  
  &[type="number"] {
    -moz-appearance: textfield;
  }
}

// Focus states
.mat-mdc-form-field.mat-focused {
  .mat-mdc-form-field-outline {
    color: vars.$primary-color;
  }
}

// Error states
.mat-mdc-form-field.mat-form-field-invalid {
  .mat-mdc-form-field-label {
    color: vars.$error-color;
  }
}
