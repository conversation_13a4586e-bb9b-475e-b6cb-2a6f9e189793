{"name": "genericspatemplate", "version": "0.0.0", "scripts": {"ng": "ng", "prestart": "node aspnetcore-https", "start": "run-script-os", "start:windows": "ng serve --port 44487 --ssl --ssl-cert %APPDATA%\\ASP.NET\\https\\%npm_package_name%.pem --ssl-key %APPDATA%\\ASP.NET\\https\\%npm_package_name%.key", "start:default": "ng serve --port 44487 --ssl --ssl-cert $HOME/.aspnet/https/${npm_package_name}.pem --ssl-key $HOME/.aspnet/https/${npm_package_name}.key", "build": "ng build", "build:ssr": "ng run GenericSpaTemplate:server:dev", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/cdk": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/material": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/platform-server": "^18.2.0", "@angular/router": "^18.2.0", "run-script-os": "^1.1.6", "rxjs": "~7.8.1", "tslib": "^2.5.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.0", "@angular/cli": "^18.2.0", "@angular/compiler-cli": "^18.2.0", "@types/jasmine": "~5.1.0", "@types/node": "^20.0.0", "jasmine-core": "~5.1.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "~5.5.0"}, "overrides": {"autoprefixer": "10.4.5", "webpack": "5.81.0"}, "optionalDependencies": {}}