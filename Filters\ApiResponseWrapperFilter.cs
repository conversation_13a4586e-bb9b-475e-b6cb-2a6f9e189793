using GenericSpaTemplate.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Net;

namespace GenericSpaTemplate.Filters
{
    /// <summary>
    /// Global action filter to wrap all API responses in standardized ApiResponse format.
    /// This filter automatically wraps all controller responses in the ApiResponse format
    /// for consistent API responses throughout the application.
    /// </summary>
    /// <remarks>
    /// This filter works in conjunction with the GlobalResponseTypeFilter to provide
    /// consistent API responses and documentation. Controllers can return standard
    /// ASP.NET Core results (Ok(), NotFound(), etc.) and this filter will automatically
    /// wrap them in the ApiResponse format.
    /// </remarks>
    public class ApiResponseWrapperFilter : IActionFilter
    {
        public void OnActionExecuting(ActionExecutingContext context)
        {
            // No action before execution
        }

        public void OnActionExecuted(ActionExecutedContext context)
        {
            // Skip wrapping for special result types
            if (context.Result is FileResult || // Skip file downloads
                context.Result is PhysicalFileResult ||
                context.Result is VirtualFileResult ||
                context.Result is FileStreamResult ||
                context.Result is FileContentResult)
            {
                return;
            }

            // Skip wrapping for redirects
            if (context.Result is RedirectResult ||
                context.Result is RedirectToActionResult ||
                context.Result is RedirectToRouteResult ||
                context.Result is LocalRedirectResult)
            {
                return;
            }

            // Handle different result types
            switch (context.Result)
            {
                case ObjectResult objectResult:
                    HandleObjectResult(objectResult);
                    break;

                case StatusCodeResult statusCodeResult:
                    HandleStatusCodeResult(context, statusCodeResult);
                    break;

                case EmptyResult:
                    HandleEmptyResult(context);
                    break;

                case ContentResult contentResult:
                    HandleContentResult(context, contentResult);
                    break;

                case JsonResult jsonResult:
                    HandleJsonResult(context, jsonResult);
                    break;

                default:
                    // For any other result type, convert to a success response
                    var apiResponse = ApiResponse.SuccessResponse();
                    context.Result = new ObjectResult(apiResponse)
                    {
                        StatusCode = (int)HttpStatusCode.OK
                    };
                    break;
            }
        }

        private static void HandleObjectResult(ObjectResult objectResult)
        {
            // If the response is already an ApiResponse, do nothing
            if (objectResult.Value is ApiResponse ||
                (objectResult.Value != null && objectResult.Value.GetType().IsGenericType &&
                 objectResult.Value.GetType().GetGenericTypeDefinition() == typeof(ApiResponse<>)))
            {
                return;
            }

            // Wrap the original value in ApiResponse<T>
            var originalValue = objectResult.Value;
            var statusCode = objectResult.StatusCode ?? (int)HttpStatusCode.OK;

            // Handle null values
            if (originalValue == null)
            {
                var apiResponse = ApiResponse.SuccessResponse(null, statusCode);
                objectResult.Value = apiResponse;
                objectResult.StatusCode = statusCode;
                return;
            }

            // Create ApiResponse<T> dynamically
            var valueType = originalValue.GetType();
            var apiResponseType = typeof(ApiResponse<>).MakeGenericType(valueType);
            var successResponseMethod = apiResponseType.GetMethod("SuccessResponse",
                [valueType, typeof(string), typeof(int)]);

            if (successResponseMethod != null)
            {
                var wrappedResponse = successResponseMethod.Invoke(null, [originalValue, null, statusCode]);
                objectResult.Value = wrappedResponse;
                objectResult.StatusCode = statusCode;
            }
        }

        private static void HandleStatusCodeResult(ActionExecutedContext context, StatusCodeResult statusCodeResult)
        {
            var statusCode = statusCodeResult.StatusCode;
            ApiResponse response;

            // Create appropriate response based on status code
            if (statusCode >= 200 && statusCode < 300)
            {
                response = ApiResponse.SuccessResponse(null, statusCode);
            }
            else if (statusCode == 404)
            {
                response = ApiResponse.NotFoundResponse("Resource not found");
            }
            else
            {
                response = ApiResponse.FailureResponse("Request failed", statusCode);
            }

            context.Result = new ObjectResult(response)
            {
                StatusCode = statusCode
            };
        }

        private static void HandleEmptyResult(ActionExecutedContext context)
        {
            var apiResponse = ApiResponse.SuccessResponse();
            context.Result = new ObjectResult(apiResponse)
            {
                StatusCode = (int)HttpStatusCode.OK
            };
        }

        private static void HandleContentResult(ActionExecutedContext context, ContentResult contentResult)
        {
            var statusCode = contentResult.StatusCode ?? (int)HttpStatusCode.OK;

            // Don't wrap non-JSON content
            if (contentResult.ContentType != null && !contentResult.ContentType.Contains("json"))
            {
                return;
            }

            ApiResponse response;
            if (statusCode >= 200 && statusCode < 300)
            {
                response = ApiResponse.SuccessResponse(contentResult.Content, statusCode);
            }
            else
            {
                response = ApiResponse.FailureResponse(contentResult.Content ?? "Request failed", statusCode);
            }

            context.Result = new ObjectResult(response)
            {
                StatusCode = statusCode
            };
        }

        private static void HandleJsonResult(ActionExecutedContext context, JsonResult jsonResult)
        {
            var statusCode = jsonResult.StatusCode ?? (int)HttpStatusCode.OK;
            var originalValue = jsonResult.Value;

            // If the value is already an ApiResponse, don't wrap it again
            if (originalValue is ApiResponse ||
                (originalValue != null && originalValue.GetType().IsGenericType &&
                 originalValue.GetType().GetGenericTypeDefinition() == typeof(ApiResponse<>)))
            {
                return;
            }

            // Handle null values
            if (originalValue == null)
            {
                var apiResponse = ApiResponse.SuccessResponse(null, statusCode);
                context.Result = new ObjectResult(apiResponse)
                {
                    StatusCode = statusCode
                };
                return;
            }

            // Create ApiResponse<T> dynamically
            var valueType = originalValue.GetType();
            var apiResponseType = typeof(ApiResponse<>).MakeGenericType(valueType);
            var successResponseMethod = apiResponseType.GetMethod("SuccessResponse",
                [valueType, typeof(string), typeof(int)]);

            if (successResponseMethod != null)
            {
                var wrappedResponse = successResponseMethod.Invoke(null, [originalValue, null, statusCode]);
                context.Result = new ObjectResult(wrappedResponse)
                {
                    StatusCode = statusCode
                };
            }
        }
    }
}
