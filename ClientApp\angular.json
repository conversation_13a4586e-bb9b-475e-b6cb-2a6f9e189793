{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"GenericSpaTemplate": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"progress": false, "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "allowedCommonJsDependencies": ["oidc-client"], "assets": ["src/assets"], "styles": ["src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "GenericSpaTemplate:build:production"}, "development": {"browserTarget": "GenericSpaTemplate:build:development", "proxyConfig": "proxy.conf.js"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "GenericSpaTemplate:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist-server", "main": "src/main.ts", "tsConfig": "tsconfig.server.json"}, "configurations": {"dev": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": true}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}}}}}}, "defaultProject": "GenericSpaTemplate"}