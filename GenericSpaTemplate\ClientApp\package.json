{"name": "genericspatemplate", "version": "0.0.0", "scripts": {"ng": "ng", "prestart": "node aspnetcore-https", "start": "run-script-os", "start:windows": "ng serve --port 44447 --ssl --ssl-cert %APPDATA%\\ASP.NET\\https\\%npm_package_name%.pem --ssl-key %APPDATA%\\ASP.NET\\https\\%npm_package_name%.key", "start:default": "ng serve --port 44447 --ssl --ssl-cert $HOME/.aspnet/https/${npm_package_name}.pem --ssl-key $HOME/.aspnet/https/${npm_package_name}.key", "build": "ng build", "build:ssr": "ng run GenericSpaTemplate:server:dev", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^15.2.8", "@angular/common": "^15.2.8", "@angular/compiler": "^15.2.8", "@angular/core": "^15.2.8", "@angular/forms": "^15.2.8", "@angular/platform-browser": "^15.2.8", "@angular/platform-browser-dynamic": "^15.2.8", "@angular/platform-server": "^15.2.8", "@angular/router": "^15.2.8", "bootstrap": "^5.2.3", "jquery": "^3.6.4", "oidc-client": "^1.11.5", "popper.js": "^1.16.0", "run-script-os": "^1.1.6", "rxjs": "~7.8.1", "tslib": "^2.5.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.7", "@angular/cli": "^15.2.7", "@angular/compiler-cli": "^15.2.8", "@types/jasmine": "~4.3.1", "@types/jasminewd2": "~2.0.10", "@types/node": "^18.16.3", "jasmine-core": "~4.6.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.0.0", "typescript": "~4.9.5"}, "overrides": {"autoprefixer": "10.4.5", "webpack": "5.81.0"}, "optionalDependencies": {}}