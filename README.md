# Generic SPA Template

A comprehensive Angular and .NET SPA template extracted from production-ready application patterns.

## 🏗️ Architecture Overview

This template provides a robust foundation for building Single Page Applications with:

- **Frontend**: Angular 18+ with TypeScript, SCSS, Angular Material
- **Backend**: .NET 9 Web API with clean architecture patterns
- **Styling**: Modern, minimalist design with Material Design integration
- **API**: Standardized response patterns with automatic wrapping
- **Components**: Reusable form inputs and layout components

## 📁 Project Structure

```
GenericSpaTemplate/
├── frontend/                    # Angular application
│   ├── src/
│   │   ├── app/
│   │   │   ├── core/           # Core services and models
│   │   │   ├── shared/         # Reusable components
│   │   │   ├── layout/         # Layout components
│   │   │   ├── features/       # Feature modules
│   │   │   └── app.module.ts
│   │   ├── assets/             # Static assets and SCSS
│   │   └── styles.scss         # Global styles
│   ├── angular.json
│   ├── package.json
│   └── tsconfig.json
├── backend/                     # .NET Web API
│   ├── Controllers/            # API controllers
│   ├── Models/                 # DTOs and response models
│   ├── Filters/                # Action filters
│   ├── Middleware/             # Custom middleware
│   ├── Program.cs              # Application entry point
│   └── appsettings.json        # Configuration
└── docs/                       # Documentation
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- .NET 9 SDK
- Angular CLI (`npm install -g @angular/cli`)

### Setup Instructions

1. **Clone and setup the template:**
   ```bash
   git clone <repository-url>
   cd GenericSpaTemplate
   ```

2. **Backend Setup:**
   ```bash
   cd backend
   dotnet restore
   dotnet run
   ```
   API will be available at `https://localhost:7000`

3. **Frontend Setup:**
   ```bash
   cd frontend
   npm install
   ng serve
   ```
   Application will be available at `http://localhost:4200`

## 🎨 Features

### Frontend Features
- ✅ **Layout System**: Header, Footer, and responsive layout
- ✅ **Reusable Components**: Form inputs, buttons, cards
- ✅ **SCSS Architecture**: Variables, mixins, and component styles
- ✅ **Angular Material**: Integrated grid system and components
- ✅ **Routing**: Configured routing with lazy loading
- ✅ **Services**: HTTP client, validation, and state management
- ✅ **TypeScript**: Strict typing and modern ES features

### Backend Features
- ✅ **Generic API Responses**: Standardized response wrapper
- ✅ **Exception Handling**: Global exception middleware
- ✅ **CORS Configuration**: Ready for Angular frontend
- ✅ **Swagger/OpenAPI**: API documentation
- ✅ **Logging**: Structured logging with Serilog
- ✅ **Clean Architecture**: Separation of concerns

## 📖 Documentation

- [Architecture Guide](docs/architecture.md)
- [Component Usage](docs/components.md)
- [Styling Guide](docs/styling.md)
- [API Documentation](docs/api.md)
- [Development Workflow](docs/development.md)

## 🛠️ Development

### Adding New Components
```bash
cd frontend
ng generate component shared/components/my-component
```

### Adding New API Endpoints
1. Create controller in `backend/Controllers/`
2. Add models in `backend/Models/`
3. Update Swagger documentation

### Styling Guidelines
- Use SCSS exclusively
- Follow BEM naming convention
- Utilize Angular Material grid classes
- Maintain responsive design principles

## 🧪 Testing

### Frontend Testing
```bash
cd frontend
npm test                # Unit tests
npm run e2e            # End-to-end tests
```

### Backend Testing
```bash
cd backend
dotnet test
```

## 📦 Build and Deployment

### Development Build
```bash
# Frontend
cd frontend && ng build

# Backend
cd backend && dotnet build
```

### Production Build
```bash
# Frontend
cd frontend && ng build --configuration production

# Backend
cd backend && dotnet publish -c Release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This template is provided as-is for development purposes.

---

**Built with ❤️ for rapid SPA development**
