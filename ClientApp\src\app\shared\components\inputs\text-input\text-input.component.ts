import { Component, forwardRef, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, AbstractControl, FormControl } from '@angular/forms';

@Component({
  selector: 'app-text-input',
  templateUrl: './text-input.component.html',
  styleUrls: ['./text-input.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TextInputComponent),
      multi: true
    }
  ]
})
export class TextInputComponent implements ControlValueAccessor, OnInit {
  @Input() label: string = '';
  @Input() placeholder: string = '';
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() hint: string = '';
  @Input() name: string = '';
  @Input() id: string = '';
  @Input() control: AbstractControl | null = null;
  @Input() errorMessages: { [key: string]: string } = {};
  @Input() type: 'text' | 'password' | 'email' | 'number' | 'tel' = 'text';
  @Input() minLength?: number;
  @Input() maxLength?: number;
  @Input() pattern?: string;
  @Input() autocomplete: string = 'on';

  @Output() blur: EventEmitter<any> = new EventEmitter<any>();
  @Output() focus: EventEmitter<any> = new EventEmitter<any>();
  @Output() change: EventEmitter<any> = new EventEmitter<any>();

  value: any = null;
  touched: boolean = false;
  isDisabled: boolean = false;

  // Getter to safely cast AbstractControl to FormControl
  get formControl(): FormControl {
    return this.control as FormControl;
  }

  // ControlValueAccessor methods
  onChange: any = () => { };
  onTouched: any = () => { };

  ngOnInit(): void {
    // If we have a control, subscribe to value changes
    if (this.control) {
      this.control.valueChanges.subscribe(value => {
        this.value = value;
      });
    }
  }

  writeValue(value: any): void {
    this.value = value;
    if (this.control) {
      this.formControl.setValue(value, { emitEvent: false });
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  onBlur(event: any): void {
    this.touched = true;
    this.onTouched();

    // If we have a control, mark it as touched and dirty
    if (this.control) {
      this.formControl.markAsTouched();
      this.formControl.markAsDirty();
      this.formControl.updateValueAndValidity();
    }

    this.blur.emit(event);
  }

  onFocus(event: any): void {
    this.focus.emit(event);
  }

  onInputChange(value: any): void {
    this.value = value;
    this.onChange(value);
    this.change.emit(value);
  }

  /**
   * Get error message for the control
   */
  getErrorMessage(control: AbstractControl | null = this.control): string {
    if (!control || !control.errors || (!control.touched && !control.dirty)) {
      return '';
    }

    const errors = control.errors;
    const errorKey = Object.keys(errors)[0];

    // Check if custom error message exists
    if (this.errorMessages && this.errorMessages[errorKey]) {
      return this.errorMessages[errorKey];
    }

    // Use default error messages
    return this.getDefaultErrorMessage(errorKey, errors[errorKey]);
  }

  /**
   * Check if control has error
   */
  hasError(control: AbstractControl | null = this.control): boolean {
    return !!control && !!control.errors && (!!control.touched || !!control.dirty);
  }

  /**
   * Get default error message based on validation error
   */
  private getDefaultErrorMessage(errorKey: string, errorValue: any): string {
    switch (errorKey) {
      case 'required':
        return `${this.label || 'This field'} is required`;
      case 'email':
        return 'Please enter a valid email address';
      case 'minlength':
        return `Minimum length is ${errorValue.requiredLength} characters`;
      case 'maxlength':
        return `Maximum length is ${errorValue.requiredLength} characters`;
      case 'pattern':
        return 'Please enter a valid format';
      case 'min':
        return `Minimum value is ${errorValue.min}`;
      case 'max':
        return `Maximum value is ${errorValue.max}`;
      default:
        return 'Invalid input';
    }
  }
}
