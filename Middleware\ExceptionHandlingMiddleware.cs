using GenericSpaTemplate.Models;
using System.Net;
using System.Text.Json;

namespace GenericSpaTemplate.Middleware
{
    /// <summary>
    /// Global exception handling middleware that catches unhandled exceptions
    /// and returns them in a standardized ApiResponse format.
    /// </summary>
    public class ExceptionHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ExceptionHandlingMiddleware> _logger;

        public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            var response = exception switch
            {
                ArgumentException => CreateErrorResponse(
                    "Invalid argument provided",
                    HttpStatusCode.BadRequest,
                    exception.Message),

                UnauthorizedAccessException => CreateErrorResponse(
                    "Unauthorized access",
                    HttpStatusCode.Unauthorized,
                    exception.Message),

                KeyNotFoundException => CreateErrorResponse(
                    "Resource not found",
                    HttpStatusCode.NotFound,
                    exception.Message),

                NotImplementedException => CreateErrorResponse(
                    "Feature not implemented",
                    HttpStatusCode.NotImplemented,
                    exception.Message),

                TimeoutException => CreateErrorResponse(
                    "Request timeout",
                    HttpStatusCode.RequestTimeout,
                    exception.Message),

                _ => CreateErrorResponse(
                    "An internal server error occurred",
                    HttpStatusCode.InternalServerError,
                    "Please try again later or contact support if the problem persists")
            };

            context.Response.StatusCode = response.StatusCode;

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(jsonResponse);
        }

        private static ApiResponse CreateErrorResponse(string message, HttpStatusCode statusCode, string? details = null)
        {
            var response = ApiResponse.FailureResponse(message, (int)statusCode);
            
            if (!string.IsNullOrEmpty(details))
            {
                response.Errors = new Dictionary<string, string[]>
                {
                    ["details"] = [details]
                };
            }

            return response;
        }
    }
}
